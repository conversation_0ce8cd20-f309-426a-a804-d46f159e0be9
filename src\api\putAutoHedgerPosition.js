import axios from 'api/getAxios';
import { encodeCurrencyPair } from 'lib/utils';

export default async (currencyPairId, position) => {
  const encodedCcyPairId = encodeCurrencyPair(currencyPairId);
  const url = `/autohedger/${encodedCcyPairId}/position`;
  const { data } = await axios({
    headers: { 'Content-Type': 'text/plain' },
    method: 'put',
    url,
    data: position,
  });
  return data;
};
