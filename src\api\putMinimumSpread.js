import axios from 'api/getAxios';
import { encodeCurrencyPair } from 'lib/utils';

export default async (currencyPairId, spread) => {
  const encodedCcyPairId = encodeCurrencyPair(currencyPairId);
  const url = `/currency-pair/${encodedCcyPairId}/minimum-spread`;
  const { data } = await axios({
    headers: { 'Content-Type': 'application/json' },
    method: 'put',
    url,
    data: spread,
  });
  return data;
};
