services:

  wtaunit-1:
    container_name: wtaunit-1
    image: wtaunit:latest
    environment:
      - instanceId=1
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=30s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
      - WTA4UI_HOST_PORT=7090
      - WTA4DUI_HOST_PORT=7091
    volumes:
      - /srv/docker-data/wtaunit/apps:/apps
      - /srv/docker-data/wtaunit/var/wta4-ld-1:/apps/wta4/var
    ports:
      - 7090:8090
      - 7091:8091
      - 7092:9900
      - 7093:9901
      - 7094:9898
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - wtaunit

  wtaunit-2:
    container_name: wtaunit-2
    image: wtaunit:latest
    environment:
      - instanceId=2
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=30s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
      - WTA4UI_HOST_PORT=7190
      - WTA4DUI_HOST_PORT=7191
    volumes:
      - /srv/docker-data/wtaunit/apps:/apps
      - /srv/docker-data/wtaunit/var/wta4-ld-2:/apps/wta4/var
    ports:
      - 7190:8090
      - 7191:8091
      - 7192:9900
      - 7193:9901
      - 7194:9898
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - wtaunit

networks:
  wtaunit:
    name: wtaunit
