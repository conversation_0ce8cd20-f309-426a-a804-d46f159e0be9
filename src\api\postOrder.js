import qs from 'qs';
import axios from 'api/getAxios';
import { convert } from 'lib/utils';

export default async (order) => {
  const {
    product: { value: productId, equivalence, baseQuantityPrecision },
    productQuantity,
    ...rest
  } = order;
  const baseQuantity = convert(productQuantity, equivalence, baseQuantityPrecision);

  const { data } = await axios({
    method: 'post',
    url: '/order',
    data: qs.stringify({
      productId,
      baseQuantity,
      productQuantity,
      ...rest,
    }, {
      skipNulls: true,
    }),
  });
  return data;
};
