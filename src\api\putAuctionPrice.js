import axios from 'api/getAxios';
import { encodeCurrencyPair, encodeAuctionSession } from 'lib/utils';

export default async (currencyPairId, auctionSession, price) => {
  const encodedCcyPairId = encodeCurrencyPair(currencyPairId);
  const encodedAuctionSession = encodeAuctionSession(auctionSession);
  const url = `/auction/${encodedCcyPairId}/${encodedAuctionSession}/session-price`;
  const { data } = await axios({
    method: 'put',
    url,
    data: { price },
  });
  return data;
};
