import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { prop } from 'ramda';
import { getCurrencyPairs } from 'modules/busConfig';
import { getCurrencyPairsDisplayNames } from 'modules/user';

import Component from 'lib/components/CurrencyPairSelector';

const mapStateToProps = createStructuredSelector({
  ccyPairs: getCurrencyPairs,
  displayNames: getCurrencyPairsDisplayNames,
});

const mergeProps = (stateProps, _dispatchProps, { value, setValue }) => ({
  ...stateProps,
  value,
  setValue: (selectedValue) => setValue(prop('id', selectedValue)),
});

export default connect(mapStateToProps, null, mergeProps)(Component);
