import { useSelector, useDispatch } from 'react-redux';
import ViewIcon from '@mui/icons-material/VisibilityOutlined';
import { useTranslation } from 'react-i18next';
import { isNil } from 'ramda';
import {
  getCategories,
  getCategorySpread,
  getSelectedCurrencyPair,
  setSelectedCurrencyPair,
  getSelectedCurrencyPairCategory as getSelectedCategory,
  setSelectedCurrencyPairCategory as setSelectedCategory,
  setCategoryBUsView,
} from 'modules/busConfig';
import GenericTable from 'lib/components/GenericTable';
import { renderAsCustom } from 'lib/components/GenericTable/Table/Cell/CellRenderers';
import CPSelector from './CPSelector';
import CategorySelector from './CategorySelector';
import Action from './Action';
import CategorySpreadEditor from './CategorySpreadEditor';
import CategoryBUs from './CategoryBUs';
import {
  Wrapper, <PERSON><PERSON><PERSON>rapper, Styled<PERSON><PERSON>on, Selector<PERSON>rapper, ButtonWrapper,
} from './CategoriesTab.Styles';

const CategoriesTab = () => {
  const selectedCurrencyPair = useSelector(getSelectedCurrencyPair);
  const selectedCategory = useSelector(getSelectedCategory);
  const categorySpread = useSelector(getCategorySpread);
  const categories = useSelector(getCategories);
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const columns = [
    {
      accessor: 'actions',
      minWidth: 30,
      renderType: renderAsCustom(),
      customContent: Action,
      percentageWidth: '10%',
      enableSorting: false,
    },
    {
      label: 'categoryConfig.upperLimit',
      accessor: 'quantity',
      minWidth: 30,
      percentageWidth: '22%',
    },
    {
      label: 'bid',
      accessor: 'bidSpread',
      minWidth: 20,
      percentageWidth: '22%',
    },
    {
      label: 'offer',
      accessor: 'offerSpread',
      minWidth: 20,
      percentageWidth: '23%',
    },
    {
      label: 'type',
      accessor: 'spreadType',
      minWidth: 20,
      percentageWidth: '23%',
    },
  ];

  return (
    <Wrapper>
      <CategorySpreadEditor />
      <CategoryBUs />
      <GenericTable
        header={(
          <HeaderWrapper>
            <SelectorWrapper>
              <CPSelector
                value={selectedCurrencyPair}
                setValue={(value) => dispatch(setSelectedCurrencyPair(value))}
              />
            </SelectorWrapper>
            <SelectorWrapper>
              <CategorySelector
                categories={categories}
                value={selectedCategory}
                setValue={(value) => dispatch(setSelectedCategory(value))}
              />
            </SelectorWrapper>
            <ButtonWrapper>
              <StyledButton
                variant="outlined"
                fullWidth
                disabled={isNil(selectedCategory)}
                id="category.viewBus"
                startIcon={<ViewIcon />}
                onClick={() => dispatch(setCategoryBUsView(true))}
              >
                {t('categoryConfig.view')}
              </StyledButton>
            </ButtonWrapper>
          </HeaderWrapper>
        )}
        data={categorySpread}
        columns={columns}
        sortOrder={[{ id: 'quantity' }]}
        filters={[]}
      />
    </Wrapper>
  );
};

export default CategoriesTab;
