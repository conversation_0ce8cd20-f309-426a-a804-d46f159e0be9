global

defaults

  timeout connect 5000
  timeout check 5000
  timeout client 30000
  timeout server 30000

frontend stats
  bind *:4444
  mode http
  stats uri /
  stats show-legends
  stats enable
  stats auth admin:adminpwd

frontend customer-ui
  bind :6090
  mode http
  default_backend customer-ui-servers

frontend dealer-ui
  bind :6091
  mode http
  default_backend dealer-ui-servers

frontend pricing-fixsessions
  bind :9900
  mode tcp
  default_backend pricing-fix-servers

frontend trading-fixsessions
  bind :9901
  mode tcp
  default_backend trading-fix-servers

frontend um-grpc
  bind :9898 proto h2
  mode http
  default_backend um-grpc-servers

peers sticktables
  bind :10000
  server wta-loadbalancer-1
  table sticky-sessions type ip size 1m

backend customer-ui-servers
  mode http
  balance roundrobin
  option httpchk GET /api/environment
  cookie CUSTOMERUNITCOOKIE insert indirect nocache
  server customer_server_1 host.docker.internal:7090 check cookie unit1
  server customer_server_2 host.docker.internal:7190 check cookie unit2

backend dealer-ui-servers
  mode http
  balance roundrobin
  option httpchk GET /api/environment
  cookie DEALER<PERSON>ITCOOKIE insert indirect nocache
  server dealer_server_1 host.docker.internal:7091 check cookie unit1
  server dealer_server_2 host.docker.internal:7191 check cookie unit2

backend pricing-fix-servers
  mode tcp
  balance roundrobin
  server pricing_fix_session_1 host.docker.internal:7092 check
  server pricing_fix_session_2 host.docker.internal:7192 check

backend trading-fix-servers
  mode tcp
  balance roundrobin
  server trading_fix_session_1 host.docker.internal:7093 check
  server trading_fix_session_2 host.docker.internal:7193 check

backend um-grpc-servers
  mode http
  balance roundrobin
  stick match src table sticktables/sticky-sessions
  stick store-request src table sticktables/sticky-sessions
  server um_grpc_server_1 host.docker.internal:7094 check proto h2
  server um_grpc_server_2 host.docker.internal:7194 check proto h2
