import axios from 'api/getAxios';
import { encodeCurrencyPair } from 'lib/utils';

export default async (currencyPairId, tradingStatus) => {
  const encodedCcyPairId = encodeCurrencyPair(currencyPairId);
  const url = `/currency-pair/${encodedCcyPairId}/trading-status`;
  const { data } = await axios({
    headers: { 'Content-Type': 'text/plain' },
    method: 'put',
    url,
    data: tradingStatus,
  });
  return data;
};
