name: wtadev
services:

  ld1:
    container_name: wtald1
    image: wtaunit:latest
    network_mode: host
    environment:
      - instanceId=1
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=30s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
    volumes:
      - ./wtald1:/apps

  ld2:
    container_name: wtald2
    image: wtaunit:latest
    network_mode: host
    environment:
      - instanceId=2
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=30s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
    volumes:
      - ./wtald2:/apps

  eventbus:
    container_name: wtaeb
    image: apache/activemq-artemis:2.39.0
    network_mode: host
    environment:
      - ARTEMIS_USER=admin
      - ARTEMIS_PASSWORD=jamgo
      - ANONYMOUS_LOGIN=true
    volumes:
      - wtaeb-data:/var/lib/artemis-instance

  loadbalancer:
    container_name: wtalb
    image: haproxy:lts-alpine
    network_mode: host
    volumes:
      - ./wtalb:/usr/local/etc/haproxy

  database:
    container_name: wtadb
    image: mysql:8.0.37
    network_mode: host
    environment:
      - MYSQL_ROOT_PASSWORD=sys123
      - MYSQL_DATABASE=wta4
    volumes:
      - wtadb-data:/var/lib/mysql

volumes:
  wtaeb-data:
  wtadb-data:
