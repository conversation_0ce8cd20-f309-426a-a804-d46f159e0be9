#!/bin/bash

log() {
  echo "$(date '+%Y-%m-%d %H:%M:%S.%3N') - $1"
}

terminate() {
  log "Caught SIGTERM, stopping applications"
  kill $WTA4DUI_PID
  kill $WTA4UI_PID
  kill $WTA4_PID
}

log "Starting with environment:"
echo regionId=$regionId
echo instanceId=$instanceId
echo WTA4_STARTED_WAIT_TIME=$WTA4_STARTED_WAIT_TIME
echo WTA4_JAVA_ARGS=$WTA4_JAVA_ARGS
echo WTA4UI_JAVA_ARGS=$WTA4UI_JAVA_ARGS
echo WTA4DUI_JAVA_ARGS=$WTA4DUI_JAVA_ARGS

cd /apps/wta4
WTA4_JAR=$(find . -maxdepth 1 -type f -regex "^\./\(wta4\|wb2hkg\|wb2dxb\)-[0-9].*.jar" | head -n 1)
if [[ -n "$WTA4_JAR" ]]; then
  log "Starting $WTA4_JAR"
  WTA4_RUN_ID="wta4.$regionId.$instanceId.$(date +%Y-%m-%d-%H-%M-%S)"
  nohup java  -Dspring.profiles.active=wtaunit \
    -DappId=wta4 \
    $WTA4_JAVA_ARGS \
    -jar $WTA4_JAR > logs/$WTA4_RUN_ID.stdout 2>&1 &
  WTA4_PID=$!
  log "WTA4 launched with PID=$WTA4_PID, waiting $WTA4_STARTED_WAIT_TIME..."
  sleep $WTA4_STARTED_WAIT_TIME
else
  log "WTA4 jar not found, skipping"
fi

cd /apps/wta4ui
WTA4UI_JAR=$(find . -maxdepth 1 -type f -name "wta4ui-*.jar" | head -n 1)
if [[ -n "$WTA4UI_JAR" ]]; then
  log "Starting $WTA4UI_JAR"
  WTA4UI_RUN_ID="wta4ui.$regionId.$instanceId.$(date +%Y-%m-%d-%H-%M-%S)"
  nohup java -Dspring.profiles.active=wtaunit \
    -DappId=wta4ui \
    "$WTA4UI_JAVA_ARGS" \
    -jar $WTA4UI_JAR > logs/$WTA4UI_RUN_ID.stdout 2>&1 &
  WTA4UI_PID=$!
  log "Dealer UI launched with PID=$WTA4UI_PID"
else
  log "Customer UI jar not found, skipping"
fi

cd /apps/wta4-dealer-ui
WTA4DUI_JAR=$(find . -maxdepth 1 -type f -name "wta4-dealer-ui-*.jar" | head -n 1)
if [[ -n "$WTA4DUI_JAR" ]]; then
  log "Starting $WTA4DUI_JAR"
  WTA4DUI_RUN_ID="wta4-dealer-ui.$regionId.$instanceId.$(date +%Y-%m-%d-%H-%M-%S)"
  nohup java -Dspring.profiles.active=wtaunit \
    -DappId=wta4-dealer-ui \
    $WTA4DUI_JAVA_ARGS \
    -jar $WTA4DUI_JAR > logs/$WTA4DUI_RUN_ID.stdout 2>&1 &
  WTA4DUI_PID=$!
  log "Dealer UI launched with PID=$WTA4DUI_PID"
else
  log "Dealer UI jar not found, skipping"
fi


log "All applications started, see logs in /apps"
trap terminate SIGTERM
wait $WTA4DUI_PID
wait $WTA4UI_PID
wait $WTA4_PID
log "All applications stopped"
