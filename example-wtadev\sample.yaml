output: output
templates: templates
loadbalancer:
  data:
    - service: wta.customer.http
      host: localhost
      port: 8433
      protocol: https
    - service: wta.dealer.http
      host: localhost
      port: 8434
      protocol: https
    - service: wta.core.grpc
      host: localhost
      port: 4435
      protocol: tcp
    - service: wta.core.fix.trading
      host: localhost
      port: 9435
      protocol: tcp
    - service: wta.core.fix.streaming
      host: localhost
      port: 9436
      protocol: tcp
    - service: eventbus
      host: localhost
      port: 61616
wtaunits:
  - id: ld1
    appId: wta
    regionId: ld
    instanceId: 1
    customer:
      http:
        host: localhost
        port: 8113
      jmx:
        host: localhost
        port: 5113
    dealer:
      http:
        host: localhost
        port: 8114
      jmx:
        host: localhost
        port: 5114
    core:
      http:
        host: localhost
        port: 8115
      jmx:
        host: localhost
        port: 5115
      grpc:
        host: localhost
        port: 4115
      fix.trading:
        host: localhost
        port: 9110
      fix.streaming:
        host: localhost
        port: 9111
  - id: ld2
    appId: wta
    regionId: ld
    instanceId: 2
    customer:
      http:
        host: localhost
        port: 8123
      jmx:
        host: localhost
        port: 5123
    dealer:
      http:
        host: localhost
        port: 8124
      jmx:
        host: localhost
        port: 5124
    core:
      http:
        host: localhost
        port: 8125
      jmx:
        host: localhost
        port: 5125
      grpc:
        host: localhost
        port: 4125
      fix.trading:
        host: localhost
        port: 9120
      fix.streaming:
        host: localhost
        port: 9121
eventbus:
  - host: localhost
    port: 6616
database:
  jdbc-url: *******************************
  username: user
  password: pass
prometheus:
grafana:
