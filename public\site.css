@font-face {
    font-family: "Lato";
    src: url("/fonts/LatoLatin-Regular.woff2") format("woff2"),
    url("/fonts/LatoLatin-Regular.woff") format("woff"),
    url("/fonts/LatoLatin-Regular.eot") format("eot"),
    url("/fonts/LatoLatin-Regular.ttf") format('truetype');
}

@font-face {
    font-family: "Lato";
    font-weight: bold;
    src: url("/fonts/LatoLatin-Bold.woff2") format("woff2"),
    url("/fonts/LatoLatin-Bold.woff") format("woff"),
    url("/fonts/LatoLatin-Bold.eot") format("eot"),
    url("/fonts/LatoLatin-Bold.ttf") format('truetype');
}

@font-face {
    font-family: "Volhov";
    src: url("/fonts/volkhov-v11-latin-regular.woff2") format("woff2"),
    url("/fonts/volkhov-v11-latin-regular.woff") format("woff"),
    url("/fonts/volkhov-v11-latin-regular.eot") format("eot"),
    url("/fonts/volkhov-v11-latin-regular.ttf") format('truetype');
}

* {
    box-sizing: border-box;
}

html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li,
fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary, time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-family: Lato, Arial, sans-serif;
    font-size: inherit;
    vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
    display: block;
}
body {
    line-height: 1.5;
    width: 100%;
    background-color: #f3f5f8;
    font-size: 16px;
}
ol, ul {
    list-style: none;
}
blockquote, q {
    quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    /*content: none;*/
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
thead, tbody, tr, th, td {
    font: inherit;
}
input,
textarea,
button {
    font: inherit;
    line-height: 1;
}
a {
    text-decoration: none;
}
/* Scrollbar Styling */
::-webkit-scrollbar {
    height: 7px;
    width: 7px;
}
::-webkit-scrollbar-track {
    background: transparent;
}
::-webkit-scrollbar-thumb {
    background: #cad6e6;
    /* box-shadow: inset 0 0 4px rgba(0,0,0,.9); */
    border-radius: 3.5px;
}
/* Autofill font */
:-webkit-autofill::first-line {
    font-family: Lato, Arial, sans-serif;
    font-size: 16px;
}
/* Added custom clear button for search input */
input[type="search"]::-webkit-search-cancel-button {
    -webkit-appearance: none;
    height: 15px;
    width: 15px;
    margin-left: .4em;
    background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23777'><path d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/></svg>");
    cursor: pointer;
  }
