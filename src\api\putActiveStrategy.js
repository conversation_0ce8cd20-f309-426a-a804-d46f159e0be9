import axios from 'api/getAxios';
import { encodeCurrencyPair } from 'lib/utils';

export default async (currencyPairId, activeStrategy) => {
  const encodedCcyPairId = encodeCurrencyPair(currencyPairId);
  const url = `/autohedger/${encodedCcyPairId}/strategy`;
  const { data } = await axios({
    headers: { 'Content-Type': 'text/plain' },
    method: 'put',
    url,
    data: activeStrategy,
  });
  return data;
};
