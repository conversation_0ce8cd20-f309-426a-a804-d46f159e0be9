import { debugLog } from 'lib/utils';
import { getTabId } from 'lib/storage';

export default (eventSourceId, handleStatusChange, dispatch) => {
  debugLog('Creating eventsource: ', eventSourceId);
  const eventSource = new EventSource(
    `${process.env.BACKEND_URL}/${eventSourceId}-emitter?tabId=${getTabId()}`,
    { withCredentials: true },
  );
  eventSource.onopen = () => {
    debugLog('eventSource open', eventSource);
    return dispatch(handleStatusChange(eventSourceId, eventSource.readyState));
  };
  eventSource.onerror = () => {
    debugLog('eventSource error', eventSource);
    return dispatch(handleStatusChange(eventSourceId, eventSource.readyState));
  };
  debugLog('Event source created: ', eventSource);
  return eventSource;
};
