2025-06-17-12:35:11.475 [main] WARN  SqlExceptionHelper - SQL Error: 0, SQLState: 08001
2025-06-17-12:35:11.476 [main] ERROR SqlExceptionHelper - Could not create connection to database server. Attempted reconnect 3 times. Giving up.
2025-06-17-12:35:11.479 [main] WARN  JdbcEnvironmentInitiator - HHH000342: Could not obtain connection to query metadata
org.hibernate.exception.JDBCConnectionException: unable to obtain isolated JDBC connection [Could not create connection to database server. Attempted reconnect 3 times. Giving up.] [n/a]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:100)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:116)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:292)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:124)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:78)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:130)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
	at org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:221)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:189)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:171)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1431)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1502)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:75)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:954)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at ch.mks.wta4.wta4app.config.Application.main(Application.java:11)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.base/java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
Caused by: java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:111)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:98)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:90)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:64)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:74)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:885)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:810)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:438)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:189)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:61)
	... 42 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(Unknown Source)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Unknown Source)
	at java.base/java.lang.reflect.Constructor.newInstance(Unknown Source)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:104)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:88)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:829)
	... 56 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.SocksSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:62)
	... 58 common frames omitted
2025-06-17-12:35:11.489 [main] ERROR LocalContainerEntityManagerFactoryBean - Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'jakarta.persistence.jdbc.url' for common cases or 'hibernate.dialect' when a custom Dialect implementation must be provided)
2025-06-17-12:35:11.492 [main] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'jakarta.persistence.jdbc.url' for common cases or 'hibernate.dialect' when a custom Dialect implementation must be provided)
2025-06-17-12:35:11.632 [main] ERROR SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'jakarta.persistence.jdbc.url' for common cases or 'hibernate.dialect' when a custom Dialect implementation must be provided)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:954)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at ch.mks.wta4.wta4app.config.Application.main(Application.java:11)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.base/java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'jakarta.persistence.jdbc.url' for common cases or 'hibernate.dialect' when a custom Dialect implementation must be provided)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:276)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
	at org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:221)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:189)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:171)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1431)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1502)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:75)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 22 common frames omitted
Caused by: org.hibernate.HibernateException: Unable to determine Dialect without JDBC metadata (please set 'jakarta.persistence.jdbc.url' for common cases or 'hibernate.dialect' when a custom Dialect implementation must be provided)
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:191)
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:87)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentWithDefaults(JdbcEnvironmentInitiator.java:153)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:364)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:124)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:78)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:130)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	... 37 common frames omitted
2025-06-17-12:48:57.446 [main] WARN  SqlExceptionHelper - SQL Error: 0, SQLState: 08001
2025-06-17-12:48:57.448 [main] ERROR SqlExceptionHelper - Could not create connection to database server. Attempted reconnect 3 times. Giving up.
2025-06-17-12:48:57.456 [main] WARN  JdbcEnvironmentInitiator - HHH000342: Could not obtain connection to query metadata
org.hibernate.exception.JDBCConnectionException: unable to obtain isolated JDBC connection [Could not create connection to database server. Attempted reconnect 3 times. Giving up.] [n/a]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:100)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:116)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:292)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:124)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:78)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:130)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
	at org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:221)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:189)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:171)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1431)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1502)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:75)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:954)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at ch.mks.wta4.wta4app.config.Application.main(Application.java:11)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.base/java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
Caused by: java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:111)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:98)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:90)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:64)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:74)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:885)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:810)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:438)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:189)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:61)
	... 42 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(Unknown Source)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Unknown Source)
	at java.base/java.lang.reflect.Constructor.newInstance(Unknown Source)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:104)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:88)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:829)
	... 56 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.SocksSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:62)
	... 58 common frames omitted
2025-06-17-12:48:57.478 [main] ERROR LocalContainerEntityManagerFactoryBean - Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'jakarta.persistence.jdbc.url' for common cases or 'hibernate.dialect' when a custom Dialect implementation must be provided)
2025-06-17-12:48:57.487 [main] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'jakarta.persistence.jdbc.url' for common cases or 'hibernate.dialect' when a custom Dialect implementation must be provided)
2025-06-17-12:48:57.807 [main] ERROR SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'jakarta.persistence.jdbc.url' for common cases or 'hibernate.dialect' when a custom Dialect implementation must be provided)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:954)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at ch.mks.wta4.wta4app.config.Application.main(Application.java:11)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.base/java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'jakarta.persistence.jdbc.url' for common cases or 'hibernate.dialect' when a custom Dialect implementation must be provided)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:276)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
	at org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:221)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:189)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:171)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1431)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1502)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:75)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 22 common frames omitted
Caused by: org.hibernate.HibernateException: Unable to determine Dialect without JDBC metadata (please set 'jakarta.persistence.jdbc.url' for common cases or 'hibernate.dialect' when a custom Dialect implementation must be provided)
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:191)
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:87)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentWithDefaults(JdbcEnvironmentInitiator.java:153)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:364)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:124)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:78)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:130)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	... 37 common frames omitted
2025-06-17-12:54:04.236 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final com.google.common.util.concurrent.Service$State com.google.common.util.concurrent.AbstractIdleService.state()] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.238 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final void com.google.common.util.concurrent.AbstractIdleService.addListener(com.google.common.util.concurrent.Service$Listener,java.util.concurrent.Executor)] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.240 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final boolean com.google.common.util.concurrent.AbstractIdleService.isRunning()] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.243 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final java.lang.Throwable com.google.common.util.concurrent.AbstractIdleService.failureCause()] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.245 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final com.google.common.util.concurrent.Service com.google.common.util.concurrent.AbstractIdleService.startAsync()] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.248 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final void com.google.common.util.concurrent.AbstractIdleService.awaitRunning(long,java.util.concurrent.TimeUnit) throws java.util.concurrent.TimeoutException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.251 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final void com.google.common.util.concurrent.AbstractIdleService.awaitRunning(java.time.Duration) throws java.util.concurrent.TimeoutException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.253 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final void com.google.common.util.concurrent.AbstractIdleService.awaitRunning()] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.255 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final com.google.common.util.concurrent.Service com.google.common.util.concurrent.AbstractIdleService.stopAsync()] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.257 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final void com.google.common.util.concurrent.AbstractIdleService.awaitTerminated(long,java.util.concurrent.TimeUnit) throws java.util.concurrent.TimeoutException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.261 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final void com.google.common.util.concurrent.AbstractIdleService.awaitTerminated()] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:04.262 [main] WARN  CglibAopProxy - Unable to proxy interface-implementing method [public final void com.google.common.util.concurrent.AbstractIdleService.awaitTerminated(java.time.Duration) throws java.util.concurrent.TimeoutException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-06-17-12:54:07.419 [main] WARN  MarketStatusController - startUp - marketSchedule ignored! This must be on only for testing purposes
2025-06-17-12:55:50.149 [ITASimulator STARTING] ERROR EventRouter - routeEventToEventBus [ld-1] - event=Event [id=ev-0e2bf218-4dd6-463d-bc35-feabc656fec1-1, type=LP_STATUS_UPDATED, timestamp=1750157750132, userId=null, sessionId=null, instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], payload=LPStatusUpdate [lpStatusByLpId={EU3=NOT_CONNECTED, EU2=NOT_CONNECTED, JPFA=NOT_CONNECTED, GSFA=NOT_CONNECTED, DBFA=NOT_CONNECTED, JPM=CONNECTED, HSFA=NOT_CONNECTED, UBS=CONNECTED, GS=CONNECTED, ANZ=NOT_CONNECTED, EURONEXT=NOT_CONNECTED, UBFA=NOT_CONNECTED, MGFA=NOT_CONNECTED, CTFA=NOT_CONNECTED, DBR=NOT_CONNECTED, HSB=CONNECTED, MGS=CONNECTED, XLS=NOT_CONNECTED}]]
java.lang.NullPointerException: Cannot invoke "com.mkspamp.eventbus.client.IEventBusProducer.publish(byte[])" because "this.eventBusProducer" is null
	at ch.mks.wta4.um.event.EventRouter.routeEventToEventBus(EventRouter.java:101)
	at ch.mks.wta4.um.event.EventRouter.onEvent(EventRouter.java:65)
	at ch.mks.wta4.um.dealercontrol.DealerController.onLPStatusUpdate(DealerController.java:447)
	at ch.mks.wta4.um.lp.LFX2LPStatusController.lambda$onLPStatusUpdate$0(LFX2LPStatusController.java:62)
	at java.base/java.util.ArrayList.forEach(Unknown Source)
	at ch.mks.wta4.um.lp.LFX2LPStatusController.onLPStatusUpdate(LFX2LPStatusController.java:62)
	at ch.mks.wta4.ita.simulator.ITASimulator.sendLPStatusEnabledForAll(ITASimulator.java:127)
	at ch.mks.wta4.ita.simulator.ITASimulator.startUp(ITASimulator.java:115)
	at com.google.common.util.concurrent.AbstractIdleService$DelegateService.lambda$doStart$0(AbstractIdleService.java:64)
	at com.google.common.util.concurrent.Callables.lambda$threadRenaming$3(Callables.java:105)
	at java.base/java.lang.Thread.run(Unknown Source)
2025-06-17-12:55:50.159 [ITASimulator STARTING] WARN  EventRouter - onEvent [ld-1] - no handler found for event=Event [id=ev-0e2bf218-4dd6-463d-bc35-feabc656fec1-1, type=LP_STATUS_UPDATED, timestamp=1750157750132, userId=null, sessionId=null, instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], payload=LPStatusUpdate [lpStatusByLpId={EU3=NOT_CONNECTED, EU2=NOT_CONNECTED, JPFA=NOT_CONNECTED, GSFA=NOT_CONNECTED, DBFA=NOT_CONNECTED, JPM=CONNECTED, HSFA=NOT_CONNECTED, UBS=CONNECTED, GS=CONNECTED, ANZ=NOT_CONNECTED, EURONEXT=NOT_CONNECTED, UBFA=NOT_CONNECTED, MGFA=NOT_CONNECTED, CTFA=NOT_CONNECTED, DBR=NOT_CONNECTED, HSB=CONNECTED, MGS=CONNECTED, XLS=NOT_CONNECTED}]]. Doing nothing
2025-06-17-12:55:55.640 [Orchestrator STARTING] WARN  Orchestrator - acquirePrimaryStatus [ld-1]- folding to NOT_PRIMARY as there is a primary instance for this region already, regionPrimaryInstanceHeartbeat=HeartBeatMessage [timestamp=2025-06-17T12:55:52.612262754+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=ACQUIRING_PRIMARY]
2025-06-17-12:55:56.175 [MarketStatusController STARTING] WARN  MarketStatusController - updateStatusAndScheduleNextTransition - no status transition found. System will remain as ONLINE forever. marketSchedule=MarketStatusSchedule [zoneId=Z, transitions=[]]
2025-06-17-12:55:56.179 [MDSRepository STARTING] WARN  MDSRepository - loadFromStore - store not found, will be created in next save. storePath=var/mds/mds.ser
2025-06-17-12:56:02.517 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:55:55.636730678+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:56:02.499344860+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:56:12.524 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:56:02.500961156+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:56:12.499440279+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:56:18.237 [wta4-findur-bu-position-engine-worker-0] ERROR FindurAPIGatewayClient - requestCustomerBalance - urlString=http://10.161.0.22:9696/balance/20009
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-12:56:18.238 [wta4-findur-bu-position-engine-worker-0] ERROR FindurAPIGatewayClient - getCustomerBalance - accountId=20009
java.lang.RuntimeException: requestCustomerBalance - error occurred while retrieving the position, urlString=http://10.161.0.22:9696/balance/20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:72)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-12:56:18.239 [wta4-findur-bu-position-engine-worker-0] ERROR FindurBusinessUnitPositionEngine - requestFindurBalances - findurBUId=20009. Returning null
java.lang.RuntimeException: getCustomerBalance - error occurred while retrieving the position accountId=20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:44)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.lang.RuntimeException: requestCustomerBalance - error occurred while retrieving the position, urlString=http://10.161.0.22:9696/balance/20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:72)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	... 7 common frames omitted
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-12:56:18.242 [wta4-findur-bu-position-engine-worker-0] ERROR FindurBusinessUnitPositionEngine - asynchRefreshInternalBusinessUnitPositionFromFindur - buId=MKS. unable to retrieve Findur balances and no current position. Simulating empty balance from Findur
2025-06-17-12:56:22.503 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:56:12.500849359+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:56:22.497542304+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:56:32.504 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:56:22.498935206+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:56:32.497554700+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:56:42.503 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:56:32.498979798+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:56:42.497563861+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:56:52.500 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:56:42.499064976+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:56:52.495024844+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:57:02.499 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:56:52.496431594+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:57:02.495036521+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:57:12.500 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:57:02.496475603+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:57:12.495062965+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:57:22.500 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:57:12.496455104+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:57:22.492649636+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:57:32.498 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:57:22.494796092+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:57:32.492667531+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:57:42.507 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:57:32.494185521+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:57:42.492760814+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:57:52.496 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:57:42.494319325+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:57:52.490453123+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:58:02.493 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:57:52.491980322+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:58:02.490471397+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:58:12.495 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:58:02.491916214+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:58:12.490483990+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:58:22.493 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:58:12.491930099+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:58:22.488522459+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:58:32.492 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:58:22.489977573+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:58:32.488568296+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:58:42.497 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:58:42.489939679+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:58:42.488589794+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:58:52.491 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:58:42.489939679+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:58:52.486062826+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:59:02.490 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:58:52.487672631+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:59:02.486146297+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:59:12.491 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:59:02.487538487+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:59:12.486163303+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:59:22.489 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:59:12.487520298+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:59:22.484202605+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:59:32.488 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:59:22.485500266+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:59:32.484129895+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:59:42.491 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:59:32.485480794+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:59:42.484126096+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-12:59:52.486 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:59:42.485638895+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T12:59:52.481979447+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:00:02.486 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T12:59:52.483501533+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:00:02.482033298+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:00:12.485 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:00:02.483433463+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:00:12.482008211+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:00:22.484 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:00:12.483496019+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:00:22.479742747+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:00:32.484 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:00:22.480786562+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:00:32.479437981+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:00:42.482 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:00:32.480714202+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:00:42.479283591+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:00:52.480 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:00:42.480773936+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:00:52.477135882+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:01:02.482 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:00:52.478538662+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:01:02.477122767+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:01:12.482 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:01:02.478519761+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:01:12.477154372+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:01:22.479 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:01:12.478750712+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:01:22.474970132+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:01:32.478 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:01:22.476440141+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:01:32.475128725+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:01:42.480 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:01:32.476320568+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:01:42.474971066+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:01:52.476 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:01:42.476492612+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:01:52.472446981+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:02:02.478 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:01:52.473891653+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:02:02.472473142+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:02:12.477 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:02:02.473962342+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:02:12.472600008+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:02:22.474 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:02:12.473944081+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:02:22.470645641+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:02:32.476 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:02:22.472066907+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:02:32.470691908+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:02:42.479 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:02:42.472081617+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:02:42.470582376+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:02:52.473 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:02:42.472081617+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:02:52.468109414+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:03:02.472 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:02:52.469643520+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:03:02.468149002+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:03:12.472 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:03:02.469635831+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:03:12.468458552+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:03:22.471 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:03:12.469647909+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:03:22.466433538+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:03:32.470 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:03:22.467860595+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:03:32.466384350+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:03:42.470 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:03:32.467769727+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:03:42.466518376+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:03:52.467 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:03:42.467787501+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:03:52.464488618+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:04:02.469 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:03:52.465728052+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:04:02.464175616+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:04:12.471 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:04:02.465753371+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:04:12.464210291+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:04:22.479 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:04:22.463440065+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:04:22.462013964+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:04:32.466 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:04:22.463440065+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:04:32.462178214+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:04:42.466 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:04:32.463475313+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:04:42.462034713+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:04:52.464 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:04:42.463529019+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:04:52.459489+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:05:02.463 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:04:52.460917977+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:05:02.459491367+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:05:12.463 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:05:02.460945853+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:05:12.459490038+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:05:22.460 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:05:12.460879079+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:05:22.457350905+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:05:32.460 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:05:22.458833349+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:05:32.457430273+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:05:42.462 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:05:32.458891838+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:05:42.457440507+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:05:52.458 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:05:42.458800101+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:05:52.455117790+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:06:02.458 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:05:52.456496930+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:06:02.455126983+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:06:12.457 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:06:02.456499241+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:06:12.455085394+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:06:22.456 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:06:12.456474656+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:06:22.453138683+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:06:32.457 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:06:22.454702820+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:06:32.453253720+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:06:39.288 [wta4-findur-bu-position-engine-worker-1] ERROR FindurAPIGatewayClient - requestCustomerBalance - urlString=http://10.161.0.22:9696/balance/20009
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-13:06:39.290 [wta4-findur-bu-position-engine-worker-1] ERROR FindurAPIGatewayClient - getCustomerBalance - accountId=20009
java.lang.RuntimeException: requestCustomerBalance - error occurred while retrieving the position, urlString=http://10.161.0.22:9696/balance/20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:72)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-13:06:39.291 [wta4-findur-bu-position-engine-worker-1] ERROR FindurBusinessUnitPositionEngine - requestFindurBalances - findurBUId=20009. Returning null
java.lang.RuntimeException: getCustomerBalance - error occurred while retrieving the position accountId=20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:44)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.lang.RuntimeException: requestCustomerBalance - error occurred while retrieving the position, urlString=http://10.161.0.22:9696/balance/20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:72)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	... 7 common frames omitted
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-13:06:39.293 [wta4-findur-bu-position-engine-worker-1] ERROR FindurBusinessUnitPositionEngine - asynchRefreshInternalBusinessUnitPositionFromFindur <- buId=MKS. unable to retrieve Findur balances. No refresh, sticking with what we already have. currentInternalBusinessUnitPosition=InternalBusinessUnitPosition [businessUnitPosition=BusinessUnitPosition [buId=MKS, balances={}, lastBaseUpdate=*************, lastProjectedUpdate=*************], findurBalances=com.mkspamp.eventbus.model.BuBalance@3e6b229a[accountBalances=[]], lastUpdatedFromFindur=Tue Jun 17 12:56:18 CEST 2025]
2025-06-17-13:06:42.456 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:06:32.*********+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:06:42.*********+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:06:52.454 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:06:42.*********+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:06:52.450975624+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:07:02.455 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:06:52.452454822+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:07:02.451053709+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:07:12.454 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:07:02.452587968+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:07:12.451093452+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:07:22.451 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:07:12.452481982+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:07:22.448431623+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:07:32.451 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:07:22.449883590+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:07:32.448435669+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:07:42.453 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:07:32.449948236+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:07:42.448561396+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:07:52.450 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:07:42.449982762+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:07:52.446220717+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:08:02.451 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:07:52.447555005+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:08:02.446211447+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:08:12.450 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:08:02.447579646+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:08:12.446374194+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:08:22.447 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:08:12.447606794+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:08:22.444240948+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:08:32.447 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:08:22.445574644+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:08:32.444150416+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:08:42.447 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:08:32.445597582+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:08:42.444146863+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:08:52.449 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:08:52.443571273+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:08:52.442005313+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:09:02.444 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:08:52.443571273+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:09:02.441972408+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:09:12.444 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:09:02.443429715+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:09:12.442027039+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:09:22.446 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:09:22.441163626+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:09:22.439758156+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:09:32.443 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:09:22.441163626+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:09:32.439774069+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:09:42.442 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:09:32.441214165+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:09:42.439841209+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:09:52.440 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:09:42.441130730+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:09:52.437533049+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:10:02.443 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:10:02.439029682+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:10:02.437606413+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:10:12.442 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:10:12.438945041+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:10:12.437831076+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:10:22.439 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:10:12.438945041+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:10:22.435831825+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:10:32.439 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:10:22.437280660+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:10:32.435910279+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:10:42.438 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:10:32.437343096+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:10:42.435800670+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:10:52.437 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:10:42.437267879+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:10:52.433181773+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:11:02.436 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:10:52.434689716+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:11:02.433474199+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:11:12.437 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:11:02.434572413+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:11:12.433136081+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:11:22.433 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:11:12.434558387+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:11:22.431007760+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:11:32.434 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:11:22.432366877+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:11:32.431025861+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:11:42.434 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:11:32.432441437+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:11:42.431020415+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:11:52.432 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:11:42.432467259+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:11:52.429268529+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:12:02.432 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:11:52.430702539+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:12:02.429395130+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:12:12.432 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:12:02.430679163+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:12:12.429316844+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:12:22.429 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:12:12.430660144+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:12:22.426686168+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:12:32.429 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:12:22.428112700+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:12:32.426728196+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:12:42.429 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:12:32.428182931+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:12:42.426676161+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:12:52.428 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:12:42.428057006+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:12:52.424843874+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:13:02.427 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:12:52.426277457+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:13:02.424728878+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:13:12.427 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:13:02.426140644+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:13:12.424780197+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:13:22.425 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:13:12.426223134+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:13:22.422701224+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:13:32.426 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:13:22.424133099+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:13:32.422735371+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:13:42.426 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:13:32.424247386+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:13:42.422825275+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:13:52.422 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:13:42.424339506+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:13:52.419739384+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:14:02.422 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:13:52.421162723+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:14:02.419777510+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:14:12.423 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:14:02.421146629+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:14:12.419815158+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:14:22.421 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:14:12.421350280+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:14:22.417888091+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:14:32.421 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:14:22.419369878+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:14:32.418150960+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:14:42.422 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:14:32.419310584+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:14:42.418012535+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:14:52.419 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:14:42.419502691+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:14:52.415748839+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:15:02.417 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:14:52.416908896+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:15:02.415432351+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:15:12.418 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:15:02.416848165+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:15:12.415344468+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:15:22.417 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:15:12.416758659+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:15:22.413611066+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:15:32.417 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:15:22.415191786+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:15:32.413669738+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:15:42.415 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:15:32.414944903+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:15:42.413595269+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:15:52.414 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:15:42.414945449+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:15:52.411403162+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:16:02.413 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:15:52.412870586+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:16:02.411328740+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:16:12.414 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:16:02.412793432+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:16:12.411357083+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:16:22.413 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:16:12.412794389+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:16:22.409265989+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:16:32.411 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:16:22.410834143+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:16:32.409189083+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:16:39.350 [wta4-findur-bu-position-engine-worker-2] ERROR FindurAPIGatewayClient - requestCustomerBalance - urlString=http://10.161.0.22:9696/balance/20009
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-13:16:39.352 [wta4-findur-bu-position-engine-worker-2] ERROR FindurAPIGatewayClient - getCustomerBalance - accountId=20009
java.lang.RuntimeException: requestCustomerBalance - error occurred while retrieving the position, urlString=http://10.161.0.22:9696/balance/20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:72)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-13:16:39.353 [wta4-findur-bu-position-engine-worker-2] ERROR FindurBusinessUnitPositionEngine - requestFindurBalances - findurBUId=20009. Returning null
java.lang.RuntimeException: getCustomerBalance - error occurred while retrieving the position accountId=20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:44)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.lang.RuntimeException: requestCustomerBalance - error occurred while retrieving the position, urlString=http://10.161.0.22:9696/balance/20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:72)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	... 7 common frames omitted
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-13:16:39.355 [wta4-findur-bu-position-engine-worker-2] ERROR FindurBusinessUnitPositionEngine - asynchRefreshInternalBusinessUnitPositionFromFindur <- buId=MKS. unable to retrieve Findur balances. No refresh, sticking with what we already have. currentInternalBusinessUnitPosition=InternalBusinessUnitPosition [businessUnitPosition=BusinessUnitPosition [buId=MKS, balances={}, lastBaseUpdate=*************, lastProjectedUpdate=*************], findurBalances=com.mkspamp.eventbus.model.BuBalance@3e6b229a[accountBalances=[]], lastUpdatedFromFindur=Tue Jun 17 13:06:18 CEST 2025]
2025-06-17-13:16:42.412 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:16:32.*********+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:16:42.*********+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:16:52.410 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:16:42.*********+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:16:52.407245281+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:17:02.410 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:16:52.408581665+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:17:02.407458069+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:17:12.411 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:17:02.408598402+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:17:12.407323104+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:17:22.408 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:17:12.408782765+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:17:22.404922168+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:17:32.407 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:17:22.406344654+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:17:32.404845796+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:17:42.407 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:17:32.406249100+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:17:42.404916156+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:17:52.406 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:17:42.406319094+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:17:52.403387170+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:18:02.405 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:17:52.404250538+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:18:02.402951420+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:18:12.405 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:18:02.404254590+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:18:12.402930318+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:18:22.402 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:18:12.404426665+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:18:22.399917728+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:18:32.403 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:18:22.401340553+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:18:32.400343372+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:18:42.403 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:18:32.401415451+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:18:42.399957844+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:18:52.400 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:18:42.401454168+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:18:52.398477018+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:19:02.401 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:18:52.399912081+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:19:02.398497020+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:19:12.401 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:19:02.399953786+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:19:12.398570482+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:19:22.398 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:19:12.399971777+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:19:22.395826787+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:19:32.399 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:19:22.397527702+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:19:32.395865985+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:19:42.398 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:19:32.397301346+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:19:42.395965842+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:19:52.397 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:19:42.397277980+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:19:52.393738826+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:20:02.396 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:19:52.395211831+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:20:02.393814476+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:20:12.396 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:20:02.395186013+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:20:12.393720253+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:20:22.394 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:20:12.395263629+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:20:22.391447284+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:20:32.394 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:20:22.392918532+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:20:32.391629240+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:20:42.394 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:20:32.392927763+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:20:42.391739056+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:20:52.392 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:20:42.392987739+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:20:52.389102823+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:21:02.393 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:20:52.390528956+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:21:02.389268789+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:21:12.391 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:21:02.390566208+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:21:12.389126009+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:21:22.389 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:21:12.390525040+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:21:22.387189450+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:21:32.390 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:21:22.388710102+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:21:32.387235240+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:21:42.389 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:21:32.389008932+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:21:42.387287864+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:21:52.387 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:21:42.388651084+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:21:52.384773739+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:22:02.389 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:21:52.386189741+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:22:02.385204714+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:22:12.388 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:22:02.386423829+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:22:12.384868788+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:22:22.385 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:22:12.386280594+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:22:22.383116339+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:22:32.385 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:22:22.384463442+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:22:32.383118062+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:22:42.386 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:22:32.384589107+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:22:42.383242313+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:22:52.382 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:22:42.384537629+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:22:52.380120249+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:23:02.383 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:22:52.381576389+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:23:02.380071840+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:23:12.383 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:23:02.381513300+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:23:12.380248956+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:23:22.380 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:23:12.381531617+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:23:22.378748265+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:23:32.381 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:23:22.380012658+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:23:32.378977437+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:23:42.381 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:23:32.380123396+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:23:42.378624130+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:23:52.378 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:23:42.380081736+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:23:52.376218538+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:24:02.378 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:23:52.377605402+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:24:02.376212027+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:24:12.378 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:24:02.377595919+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:24:12.376192729+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:24:22.377 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:24:12.377578564+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:24:22.374463012+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:24:32.376 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:24:22.375754915+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:24:32.374336765+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:24:42.376 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:24:32.375789704+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:24:42.374323502+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:24:52.375 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:24:42.375694987+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:24:52.372174849+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:25:02.373 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:24:52.373576849+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:25:02.372103925+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:25:12.374 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:25:02.373537753+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:25:12.372214063+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:25:22.371 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:25:12.373543474+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:25:22.369884279+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:25:32.372 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:25:22.371338615+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:25:32.369805299+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:25:42.371 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:25:32.371320748+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:25:42.369821120+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:25:52.370 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:25:42.371322417+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:25:52.367948267+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:26:02.369 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:25:52.369367803+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:26:02.367961800+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:26:12.370 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:26:02.369388673+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:26:12.367899561+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:26:22.367 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:26:12.369369969+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:26:22.365778810+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:26:32.368 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:26:22.367226142+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:26:32.365959790+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:26:39.321 [wta4-findur-bu-position-engine-worker-3] ERROR FindurAPIGatewayClient - requestCustomerBalance - urlString=http://10.161.0.22:9696/balance/20009
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-13:26:39.322 [wta4-findur-bu-position-engine-worker-3] ERROR FindurAPIGatewayClient - getCustomerBalance - accountId=20009
java.lang.RuntimeException: requestCustomerBalance - error occurred while retrieving the position, urlString=http://10.161.0.22:9696/balance/20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:72)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-13:26:39.323 [wta4-findur-bu-position-engine-worker-3] ERROR FindurBusinessUnitPositionEngine - requestFindurBalances - findurBUId=20009. Returning null
java.lang.RuntimeException: getCustomerBalance - error occurred while retrieving the position accountId=20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:44)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.requestFindurBalances(FindurBusinessUnitPositionEngine.java:243)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.asynchRefreshInternalBusinessUnitPositionFromFindur(FindurBusinessUnitPositionEngine.java:202)
	at ch.mks.wta4.um.exposure.FindurBusinessUnitPositionEngine.lambda$refreshInternalBusinessUnitPositionFromFindur$4(FindurBusinessUnitPositionEngine.java:174)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.lang.RuntimeException: requestCustomerBalance - error occurred while retrieving the position, urlString=http://10.161.0.22:9696/balance/20009
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:72)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.getCustomerBalance(FindurAPIGatewayClient.java:39)
	... 7 common frames omitted
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://10.161.0.22:9696/balance/20009": Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at ch.mks.wta4.services.position.FindurAPIGatewayClient.requestCustomerBalance(FindurAPIGatewayClient.java:58)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.connect0(Native Method)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.Net.connect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at java.base/sun.net.NetworkClient.doConnect(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.openServer(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.<init>(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.http.HttpClient.New(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(Unknown Source)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(Unknown Source)
	at org.springframework.http.client.SimpleClientHttpRequest.executeInternal(SimpleClientHttpRequest.java:79)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 11 common frames omitted
2025-06-17-13:26:39.324 [wta4-findur-bu-position-engine-worker-3] ERROR FindurBusinessUnitPositionEngine - asynchRefreshInternalBusinessUnitPositionFromFindur <- buId=MKS. unable to retrieve Findur balances. No refresh, sticking with what we already have. currentInternalBusinessUnitPosition=InternalBusinessUnitPosition [businessUnitPosition=BusinessUnitPosition [buId=MKS, balances={}, lastBaseUpdate=*************, lastProjectedUpdate=*************], findurBalances=com.mkspamp.eventbus.model.BuBalance@3e6b229a[accountBalances=[]], lastUpdatedFromFindur=Tue Jun 17 13:16:18 CEST 2025]
2025-06-17-13:26:42.368 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:26:32.*********+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:26:42.*********+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:26:52.365 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:26:42.*********+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:26:52.363774823+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:27:02.366 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:26:52.365173671+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:27:02.363832493+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:27:12.366 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:27:02.365179318+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:27:12.363763098+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:27:22.363 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:27:12.365260882+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:27:22.361604233+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:27:32.364 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:27:22.363363189+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:27:32.361654914+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:27:42.363 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:27:32.363061457+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:27:42.361659369+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:27:52.361 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:27:42.363105307+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:27:52.359404999+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:28:02.362 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:27:52.360952234+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:28:02.359400969+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:28:12.361 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:28:02.360904635+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:28:12.359334734+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:28:22.359 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:28:12.360812457+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:28:22.356836620+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:28:32.358 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:28:22.358202692+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:28:32.356777385+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:28:42.358 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:28:32.358212856+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:28:42.356810026+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:28:52.357 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:28:42.358261938+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:28:52.354877108+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:29:02.357 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:28:52.356339731+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:29:02.354973624+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:29:12.357 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:29:02.356547730+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:29:12.354960920+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:29:22.355 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:29:12.356381312+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:29:22.352828127+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:29:32.355 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:29:22.354396847+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:29:32.352950474+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:29:42.354 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:29:32.354292453+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:29:42.352829076+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:29:52.352 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:29:42.354232066+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:29:52.350889955+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:30:02.352 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:29:52.352107111+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:30:02.350685368+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:30:12.353 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:30:02.352115161+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:30:12.350673528+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:30:22.351 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:30:12.352158817+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:30:22.348835963+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:30:32.351 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:30:22.350273817+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:30:32.348828490+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:30:42.353 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:30:32.350272122+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:30:42.348876364+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:30:52.349 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:30:42.351861618+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:30:52.346931192+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:31:02.349 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:30:52.348252234+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:31:02.346862238+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:31:12.348 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:31:02.348283550+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:31:12.346815837+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:31:22.347 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:31:12.348243702+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:31:22.344175974+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:31:32.346 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:31:22.345593310+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:31:32.344205346+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:31:42.346 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:31:32.345655438+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:31:42.344236752+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:31:52.344 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:31:42.345625421+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:31:52.342751488+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:32:02.345 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:31:52.344101178+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:32:02.342728214+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:32:12.345 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:32:02.344206460+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:32:12.342726845+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:32:22.342 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:32:12.344090300+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:32:22.340445625+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:32:32.342 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:32:22.341801060+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:32:32.340555489+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:32:42.342 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:32:32.341809268+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:32:42.340372904+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:32:52.340 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:32:42.341807260+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:32:52.338215888+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:33:02.340 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:32:52.339685829+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:33:02.338262445+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:33:12.340 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:33:02.339671015+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:33:12.338331359+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:33:22.339 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:33:12.339713139+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:33:22.335980850+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:33:32.338 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:33:22.337431318+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:33:32.336038411+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:33:42.338 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:33:32.337383581+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:33:42.335963466+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:33:52.335 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:33:42.337374165+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:33:52.333712686+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:34:02.336 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:33:52.335120313+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:34:02.333704262+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:34:12.336 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:34:02.335164965+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:34:12.333790445+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:34:22.334 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:34:12.335202548+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:34:22.331848697+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:34:32.333 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:34:22.333216799+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:34:32.331840512+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:34:42.334 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:34:32.333322527+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:34:42.331974421+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:34:52.331 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:34:42.333271339+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:34:52.329562805+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:35:02.331 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:34:52.330949777+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:35:02.329526906+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:35:12.332 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:35:02.331000501+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:35:12.329580538+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:35:22.330 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:35:12.331046097+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:35:22.327755722+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:35:32.329 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:35:22.329142252+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:35:32.327639338+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:35:42.329 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:35:32.329019023+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:35:42.327696105+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:35:52.328 [wta4-orchestrator-heartbeat-0] ERROR Orchestrator - checkCluster [ld-1]- no PRIMARY instance found in cluster for region ld, heartbeats={InstanceInfo [appId=wta4, regionId=ld, instanceId=2]=HeartBeatMessage [timestamp=2025-06-17T13:35:42.329068339+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=2], primaryStatus=NOT_PRIMARY], InstanceInfo [appId=wta4, regionId=ld, instanceId=1]=HeartBeatMessage [timestamp=2025-06-17T13:35:52.325725454+02:00[Europe/Paris], instanceInfo=InstanceInfo [appId=wta4, regionId=ld, instanceId=1], primaryStatus=NOT_PRIMARY]}
2025-06-17-13:35:55.691 [Thread-142] ERROR AMQEventBusConsumer - onException - url=tcp://localhost:61616, destination=wta-synchronization(TOPIC)
jakarta.jms.JMSException: ActiveMQDisconnectedException[errorType=DISCONNECTED message=AMQ219015: The connection was disconnected because of server shutdown]
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:724)
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:745)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.callSessionFailureListeners(ClientSessionFactoryImpl.java:878)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.failoverOrReconnect(ClientSessionFactoryImpl.java:804)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.handleConnectionFailure(ClientSessionFactoryImpl.java:576)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$DelegatingFailureListener.connectionFailed(ClientSessionFactoryImpl.java:1417)
	at org.apache.activemq.artemis.spi.core.protocol.AbstractRemotingConnection.callFailureListeners(AbstractRemotingConnection.java:98)
	at org.apache.activemq.artemis.core.protocol.core.impl.RemotingConnectionImpl.fail(RemotingConnectionImpl.java:209)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$CloseRunnable.run(ClientSessionFactoryImpl.java:1182)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:57)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:32)
	at org.apache.activemq.artemis.utils.actors.ProcessorBase.executePendingTasks(ProcessorBase.java:68)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.activemq.artemis.utils.ActiveMQThreadFactory$1.run(ActiveMQThreadFactory.java:118)
Caused by: org.apache.activemq.artemis.api.core.ActiveMQDisconnectedException: AMQ219015: The connection was disconnected because of server shutdown
	... 7 common frames omitted
2025-06-17-13:35:55.693 [Thread-143] ERROR AMQEventBusProducer - onException - url=tcp://localhost:61616, destination=wta-booking(QUEUE)
jakarta.jms.JMSException: ActiveMQDisconnectedException[errorType=DISCONNECTED message=AMQ219015: The connection was disconnected because of server shutdown]
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:724)
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:745)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.callSessionFailureListeners(ClientSessionFactoryImpl.java:878)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.failoverOrReconnect(ClientSessionFactoryImpl.java:804)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.handleConnectionFailure(ClientSessionFactoryImpl.java:576)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$DelegatingFailureListener.connectionFailed(ClientSessionFactoryImpl.java:1417)
	at org.apache.activemq.artemis.spi.core.protocol.AbstractRemotingConnection.callFailureListeners(AbstractRemotingConnection.java:98)
	at org.apache.activemq.artemis.core.protocol.core.impl.RemotingConnectionImpl.fail(RemotingConnectionImpl.java:209)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$CloseRunnable.run(ClientSessionFactoryImpl.java:1182)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:57)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:32)
	at org.apache.activemq.artemis.utils.actors.ProcessorBase.executePendingTasks(ProcessorBase.java:68)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.activemq.artemis.utils.ActiveMQThreadFactory$1.run(ActiveMQThreadFactory.java:118)
Caused by: org.apache.activemq.artemis.api.core.ActiveMQDisconnectedException: AMQ219015: The connection was disconnected because of server shutdown
	... 7 common frames omitted
2025-06-17-13:35:55.695 [Thread-145] ERROR AMQEventBusProducer - onException - url=tcp://localhost:61616, destination=wta-synchronization(TOPIC)
jakarta.jms.JMSException: ActiveMQDisconnectedException[errorType=DISCONNECTED message=AMQ219015: The connection was disconnected because of server shutdown]
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:724)
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:745)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.callSessionFailureListeners(ClientSessionFactoryImpl.java:878)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.failoverOrReconnect(ClientSessionFactoryImpl.java:804)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.handleConnectionFailure(ClientSessionFactoryImpl.java:576)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$DelegatingFailureListener.connectionFailed(ClientSessionFactoryImpl.java:1417)
	at org.apache.activemq.artemis.spi.core.protocol.AbstractRemotingConnection.callFailureListeners(AbstractRemotingConnection.java:98)
	at org.apache.activemq.artemis.core.protocol.core.impl.RemotingConnectionImpl.fail(RemotingConnectionImpl.java:209)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$CloseRunnable.run(ClientSessionFactoryImpl.java:1182)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:57)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:32)
	at org.apache.activemq.artemis.utils.actors.ProcessorBase.executePendingTasks(ProcessorBase.java:68)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.activemq.artemis.utils.ActiveMQThreadFactory$1.run(ActiveMQThreadFactory.java:118)
Caused by: org.apache.activemq.artemis.api.core.ActiveMQDisconnectedException: AMQ219015: The connection was disconnected because of server shutdown
	... 7 common frames omitted
2025-06-17-13:35:55.695 [Thread-144] ERROR AMQEventBusConsumer - onException - url=tcp://localhost:61616, destination=findur-events(TOPIC)
jakarta.jms.JMSException: ActiveMQDisconnectedException[errorType=DISCONNECTED message=AMQ219015: The connection was disconnected because of server shutdown]
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:724)
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:745)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.callSessionFailureListeners(ClientSessionFactoryImpl.java:878)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.failoverOrReconnect(ClientSessionFactoryImpl.java:804)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.handleConnectionFailure(ClientSessionFactoryImpl.java:576)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$DelegatingFailureListener.connectionFailed(ClientSessionFactoryImpl.java:1417)
	at org.apache.activemq.artemis.spi.core.protocol.AbstractRemotingConnection.callFailureListeners(AbstractRemotingConnection.java:98)
	at org.apache.activemq.artemis.core.protocol.core.impl.RemotingConnectionImpl.fail(RemotingConnectionImpl.java:209)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$CloseRunnable.run(ClientSessionFactoryImpl.java:1182)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:57)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:32)
	at org.apache.activemq.artemis.utils.actors.ProcessorBase.executePendingTasks(ProcessorBase.java:68)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.activemq.artemis.utils.ActiveMQThreadFactory$1.run(ActiveMQThreadFactory.java:118)
Caused by: org.apache.activemq.artemis.api.core.ActiveMQDisconnectedException: AMQ219015: The connection was disconnected because of server shutdown
	... 7 common frames omitted
2025-06-17-13:35:55.695 [Thread-146] ERROR AMQEventBusConsumer - onException - url=tcp://localhost:61616, destination=wta4.ld.1(QUEUE)
jakarta.jms.JMSException: ActiveMQDisconnectedException[errorType=DISCONNECTED message=AMQ219015: The connection was disconnected because of server shutdown]
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:724)
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:745)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.callSessionFailureListeners(ClientSessionFactoryImpl.java:878)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.failoverOrReconnect(ClientSessionFactoryImpl.java:804)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.handleConnectionFailure(ClientSessionFactoryImpl.java:576)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$DelegatingFailureListener.connectionFailed(ClientSessionFactoryImpl.java:1417)
	at org.apache.activemq.artemis.spi.core.protocol.AbstractRemotingConnection.callFailureListeners(AbstractRemotingConnection.java:98)
	at org.apache.activemq.artemis.core.protocol.core.impl.RemotingConnectionImpl.fail(RemotingConnectionImpl.java:209)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$CloseRunnable.run(ClientSessionFactoryImpl.java:1182)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:57)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:32)
	at org.apache.activemq.artemis.utils.actors.ProcessorBase.executePendingTasks(ProcessorBase.java:68)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.activemq.artemis.utils.ActiveMQThreadFactory$1.run(ActiveMQThreadFactory.java:118)
Caused by: org.apache.activemq.artemis.api.core.ActiveMQDisconnectedException: AMQ219015: The connection was disconnected because of server shutdown
	... 7 common frames omitted
2025-06-17-13:35:55.701 [Thread-147] ERROR AMQEventBusProducer - onException - url=tcp://localhost:61616, destination=wta-autohedger(QUEUE)
jakarta.jms.JMSException: ActiveMQDisconnectedException[errorType=DISCONNECTED message=AMQ219015: The connection was disconnected because of server shutdown]
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:724)
	at org.apache.activemq.artemis.jms.client.ActiveMQConnection$JMSFailureListener.connectionFailed(ActiveMQConnection.java:745)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.callSessionFailureListeners(ClientSessionFactoryImpl.java:878)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.failoverOrReconnect(ClientSessionFactoryImpl.java:804)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl.handleConnectionFailure(ClientSessionFactoryImpl.java:576)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$DelegatingFailureListener.connectionFailed(ClientSessionFactoryImpl.java:1417)
	at org.apache.activemq.artemis.spi.core.protocol.AbstractRemotingConnection.callFailureListeners(AbstractRemotingConnection.java:98)
	at org.apache.activemq.artemis.core.protocol.core.impl.RemotingConnectionImpl.fail(RemotingConnectionImpl.java:209)
	at org.apache.activemq.artemis.core.client.impl.ClientSessionFactoryImpl$CloseRunnable.run(ClientSessionFactoryImpl.java:1182)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:57)
	at org.apache.activemq.artemis.utils.actors.OrderedExecutor.doTask(OrderedExecutor.java:32)
	at org.apache.activemq.artemis.utils.actors.ProcessorBase.executePendingTasks(ProcessorBase.java:68)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.activemq.artemis.utils.ActiveMQThreadFactory$1.run(ActiveMQThreadFactory.java:118)
Caused by: org.apache.activemq.artemis.api.core.ActiveMQDisconnectedException: AMQ219015: The connection was disconnected because of server shutdown
	... 7 common frames omitted
