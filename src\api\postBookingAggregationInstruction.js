import qs from 'qs';
import axios from 'api/getAxios';

export default async ({
  buId,
  currencyPairId,
  channel,
  maxPosition,
  maxTimeOpen,
  maxMarketDeviation,
}) => {
  const { data } = await axios({
    method: 'post',
    url: '/booking-aggregation-instruction',
    data: qs.stringify({
      buId,
      currencyPairId,
      channel,
      maxPosition,
      maxTimeOpen,
      maxMarketDeviation,
    }),
  });
  return data;
};
