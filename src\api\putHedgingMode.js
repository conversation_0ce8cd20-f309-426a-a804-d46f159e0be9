import axios from 'api/getAxios';
import { encodeCurrencyPair } from 'lib/utils';

export default async (currencyPairId, hedgingMode) => {
  const encodedCcyPairId = encodeCurrencyPair(currencyPairId);
  const url = `/currency-pair/${encodedCcyPairId}/hedging`;
  const { data } = await axios({
    method: 'put',
    url,
    data: {
      mode: hedgingMode,
      operation: 'ALL',
    },
  });
  return data;
};
