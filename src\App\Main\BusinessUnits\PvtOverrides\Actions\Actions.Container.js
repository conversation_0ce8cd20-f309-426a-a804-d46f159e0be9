import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { openPvtOverrideDeleteDialog, openPvtOverrideEditor } from 'modules/busConfig';
import { canUpdatePvtOverrides } from 'modules/user';
import Component from './Actions.Component';

const mapStateToProps = createStructuredSelector({
  canShowEdit: canUpdatePvtOverrides,
});

const mapDispatchToProps = ({
  openEditor: openPvtOverrideEditor,
  openDeleteDialog: openPvtOverrideDeleteDialog,
});

export default connect(mapStateToProps, mapDispatchToProps)(Component);
