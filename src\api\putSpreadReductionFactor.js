import axios from 'api/getAxios';
import { encodeCurrencyPair } from 'lib/utils';

export default async (currencyPairId, factor) => {
  const encodedCcyPairId = encodeCurrencyPair(currencyPairId);
  const url = `/currency-pair/${encodedCcyPairId}/spread-reduction-factor`;
  const { data } = await axios({
    headers: { 'Content-Type': 'application/json' },
    method: 'put',
    url,
    data: factor,
  });
  return data;
};
