<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="10 seconds">

	<!-- Suppress Logback's internal status messages.
     This line is added to reduce the logs generated due to the conditional logic below
     that determines the APP_NAME.
     Comment out or remove this line if you need to troubleshoot Logback issues. -->
	<statusListener class="ch.qos.logback.core.status.NopStatusListener" />

	<property name="LOG_HOME" value="logs" />
	<!-- Define properties for application ID, instance ID, and region ID
     These will be set from system properties or environment variables if available,
     otherwise they default to "undefined" -->
	<property name="APP_ID" value="${appId:-undefined}" />
    <property name="INSTANCE_ID" value="${instanceId:-undefined}" />
    <property name="REGION_ID" value="${regionId:-undefined}" />

<!-- Conditional logic to construct a more detailed APP_NAME if all IDs are defined -->
    <if condition='!("undefined".equals("${APP_ID}")) &amp;&amp; !("undefined".equals("${INSTANCE_ID}")) &amp;&amp; !("undefined".equals("${REGION_ID}"))'>
        <then>
            <property name="APP_NAME" value="${APP_ID}-${REGION_ID}-${INSTANCE_ID}" />
        </then>
    </if>

  	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    	<encoder>
			<pattern>%d{YYYY-MM-dd-HH:mm:ss.SSS} %X{req.xForwardedFor} [%thread] %-5level %logger{0} - %msg%n</pattern>
		</encoder>
  	</appender>

	<appender name="MAIN" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_HOME}/${APP_NAME}.log</file>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/${APP_NAME}.%d{yyyy-MM-dd}.log.gz</fileNamePattern>
			<maxHistory>30</maxHistory>
	    </rollingPolicy>
		<encoder>
			<pattern>%d{YYYY-MM-dd-HH:mm:ss.SSS} %X{req.xForwardedFor} [%thread] %-5level %logger{0} - %msg%n</pattern>
		</encoder>
	</appender>

	<appender name="ASYNC-MAIN" class="ch.qos.logback.classic.AsyncAppender">
    	<appender-ref ref="MAIN" />
  	</appender>

  	<appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_HOME}/${APP_NAME}.error.log</file>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/${APP_NAME}.%d{yyyy-MM-dd}.error.log.gz</fileNamePattern>
			<maxHistory>30</maxHistory>
	    </rollingPolicy>
	    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
    	</filter>
		<encoder>
			<pattern>%d{YYYY-MM-dd-HH:mm:ss.SSS} [%thread] %-5level %logger{0} - %msg%n</pattern>
		</encoder>
	</appender>

	<root level="INFO">
		<appender-ref ref="ASYNC-MAIN" />
		<appender-ref ref="ERROR" />
	</root>

</configuration>
