# This file must be copied to config folder (root) to be used by the app. It overrides any property set in at application or framework level
applicationId=DMFWTA4
project.build.type=UAT

#Embedded Tomcat configuration
server.servlet.context-path=/
server.port=8080
server.address=0.0.0.0
server.tomcat.accesslog.enabled=true
server.tomcat.basedir=var/tomcat
server.tomcat.accesslog.pattern=%{X-Forwarded-For}i %h %l %u %t %r %s %b

#security.require-ssl=true
#server.ssl.key-store-type=PKCS12
#server.ssl.key-store=file:config/wta4-https.p12
#server.ssl.key-store-password=

#Database connection
spring.datasource.url=***************************************************
spring.datasource.username=root
spring.datasource.password=root

#Email settings from here
mail.smtp.host=localhost
mail.smtp.port=5025
mail.smtp.from=<EMAIL>
mail.environment=DMF_DEV
#mail.tlsEnable=false
#mail.tlsVersion=TLSv1.2

#bookingSupportEmail=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

#sms server conf
#sms.domain=sms.ip-plus.net
#sms.enabled=true
#sms.override=+************,+************,+************,+34678918714

#FIX connector configurations
liquidity.provider.config=config/lfx2-fix.cfg
fix.adapter.config=config/ita-fix-adapter.cfg
findur.booking.config=config/wta4-findur.cfg

#JMX
rmiJMXHost=127.0.0.1
rmiJMXPort=10237

umRemote.secretsByClientId= {'WTA4-UI':'wta4ui-secret-dani-sdfsa897a09fasdfas0df9a70sd98f70as9df70as97df0a9sd', 'WTA4-DEALER-UI':'dealer-ui-secret-dani-4398523kjsadhfglksjdfyghwe8945t7we', 'um-remote-test':'umremote-test-dev-woiyrhkjdsfsudyfaosieyrw49283742'}
#grpc.secretsByClientId= {'WTA4-UI':'wta4ui-secret-dani-sdfsa897a09fasdfas0df9a70sd98f70as9df70as97df0a9sd', 'WTA4-DEALER-UI':'dealer-ui-secret-dani-4398523kjsadhfglksjdfyghwe8945t7we', 'um-remote-test':'umremote-test-dev-woiyrhkjdsfsudyfaosieyrw49283742'}

#Findur position service
findur.webservice.url=http://***********:8080/ConnexServlet/raw/CustPosReport/WTA-Pamp/
findur.webservice.payload=<?xml version='1.0' encoding='utf-8'?><request username="" password="" preserve-attrs='no'><customer>0</customer></request>
findur.webservice.user=webuser
findur.webservice.password=GretLive63?

#Deal ticket API enabled BU's. These BU are allowed to place DealTickets through the API.
dealTicket.api.allowed.bu.list=BBB,MKS HK,AAAA

fix.adapter.connectionType=acceptor
fix.adapter.fileStorePath=var/ita-fix-adapter/store
fix.adapter.reconnectInterval=15
fix.adapter.startDay=Saturday
fix.adapter.endDay=Saturday
fix.adapter.startTime=21:34:30
fix.adapter.endTime=21:30:30
fix.adapter.beginString=FIX.4.4
fix.adapter.useDataDictionary=Y
fix.adapter.dataDictionaryFileName=ITA-FIX.xml
fix.adapter.checkLatency=N
fix.adapter.socketAcceptAddress=127.0.0.1
fix.adapter.socketUseSSL=Y
fix.adapter.enabledProtocols=TLSv1.2
fix.adapter.socketKeyStore=default-fix-keystore.p12
fix.adapter.socketKeyStorePassword=titella

#Inactivity time out
websession.timeout.seconds=90

## Hedge Business Unit ID
hedge.businessunit.id=LFX2

spring.jpa.open-in-view=false

autohedger.hssEnabled=false

pushnotification.enabled = false


#OpenId Connect configuration
oauth2.clients.WTA4-DEALER-UI.clientId=78c8df38-38d3-4b0b-b42d-153b86a440f8
oauth2.clients.WTA4-DEALER-UI.clientSecret=*************************************
oauth2.clients.WTA4-DEALER-UI.issuer=https://sts.windows.net/713423ef-92ec-4754-89d3-b6df9b4b107f/
oauth2.clients.WTA4-DEALER-UI.keyDiscoveryUri=https://login.microsoftonline.com/common/discovery/keys
oauth2.clients.WTA4-DEALER-UI.authorizationUri=https://login.microsoftonline.com/713423ef-92ec-4754-89d3-b6df9b4b107f/oauth2/authorize
oauth2.clients.WTA4-DEALER-UI.accessTokenUri=https://login.microsoftonline.com/713423ef-92ec-4754-89d3-b6df9b4b107f/oauth2/token
oauth2.clients.WTA4-DEALER-UI.redirectUri=http://localhost:3091/login-oauth2


#Market schedule
market.status.transition.schedule=SUNDAY 18:00:00,MONDAY 17:00:00,MONDAY 18:00:00,TUESDAY 17:00:00,TUESDAY 18:00:00,WEDNESDAY 17:00:00,WEDNESDAY 18:00:00,THURSDAY 17:00:00,THURSDAY 18:00:00,FRIDAY 17:00:00

# lp ita user ID must exist in user profile login ID
lp.ita.user.id=<EMAIL>
# system user ID must exist in user profile login ID
system.user.id=<EMAIL>
# auto delaer booking ID to send to Findur
auto.dealer.booking.user.id=<EMAIL>

#Market order API enabled BU's. These BU are allowed to place Market orders through the API.
marketOrder.api.allowed.bu.list=MKS HK

exposure.feature.enabled=true
exposure.engine.price.refresh.period.seconds=60
findur.position.wta.order.cache.timetolive.millis=600000
findur.position.refresh.period.seconds=600
findur.apigateway.baseurl=http://***********:9696
findur.apigateway.apikey=uat-test-api-key

oidc.clients.WTA4-UI.clientId=wta4ui-dev
oidc.clients.WTA4-UI.clientSecret=DEKSj2reyGAdtkcEVZ2w8CfR582SDV0C
oidc.clients.WTA4-UI.issuer=https://uataccount.mkspamp.com/realms/mkspamp
oidc.clients.WTA4-UI.keyDiscoveryUri=https://uataccount.mkspamp.com/realms/mkspamp/protocol/openid-connect/certs
oidc.clients.WTA4-UI.authorizationUri=https://uataccount.mkspamp.com/realms/mkspamp/protocol/openid-connect/auth
oidc.clients.WTA4-UI.accessTokenUri=https://uataccount.mkspamp.com/realms/mkspamp/protocol/openid-connect/token
oidc.clients.WTA4-UI.redirectUri=http://localhost:3090/login-oauth2
oidc.clients.WTA4-UI.logoutUri=https://uataccount.mkspamp.com/realms/mkspamp/protocol/openid-connect/logout?client_id=wta4ui-dev
oidc.clients.WTA4-UI.accountManagementUri=https://uataccount.mkspamp.com/realms/mkspamp/account/account-security/signing-in


oidc.clients.WTA4-DEALER-UI.clientId=wta4-dealer-ui-dev
oidc.clients.WTA4-DEALER-UI.clientSecret=1jc0YCoe3ob4cF9e4WWhT7HwvCI2kugp
oidc.clients.WTA4-DEALER-UI.issuer=https://uataccount.mkspamp.com/realms/mkspamp
oidc.clients.WTA4-DEALER-UI.keyDiscoveryUri=https://uataccount.mkspamp.com/realms/mkspamp/protocol/openid-connect/certs
oidc.clients.WTA4-DEALER-UI.authorizationUri=https://uataccount.mkspamp.com/realms/mkspamp/protocol/openid-connect/auth
oidc.clients.WTA4-DEALER-UI.accessTokenUri=https://uataccount.mkspamp.com/realms/mkspamp/protocol/openid-connect/token
oidc.clients.WTA4-DEALER-UI.redirectUri=http://localhost:3091/login-oauth2
oidc.clients.WTA4-DEALER-UI.logoutUri=https://uataccount.mkspamp.com/realms/mkspamp/protocol/openid-connect/logout?client_id=wta4-dealer-ui-dev
oidc.clients.WTA4-DEALER-UI.accountManagementUri=https://uataccount.mkspamp.com/realms/mkspamp/account/account-security/signing-in
