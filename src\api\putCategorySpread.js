import axios from 'api/getAxios';
import { encodeCurrencyPair } from 'lib/utils';

export default async (categoryId, currencyPairId, qty, bid, offer, type) => {
  const encodedCcyPairId = encodeCurrencyPair(currencyPairId);
  const url = `/categories/${categoryId}/${encodedCcyPairId}/band`;
  const { data } = await axios({
    method: 'put',
    url,
    data: {
      qty, bid, offer, type,
    },
  });
  return data;
};
