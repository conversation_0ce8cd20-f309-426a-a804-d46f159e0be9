import qs from 'qs';
import axios from 'api/getAxios';
import { convert } from 'lib/utils';

export default async (orderId, order) => {
  const {
    product: { value: productId, equivalence, baseQuantityPrecision },
    productQuantity,
    ...rest
  } = order;
  const baseQuantity = convert(productQuantity, equivalence, baseQuantityPrecision);
  const url = `/order/${orderId}/update`;
  const { data } = await axios({
    method: 'put',
    url,
    data: qs.stringify({
      productId,
      baseQuantity,
      productQuantity,
      ...rest,
    }, {
      skipNulls: true,
    }),
  });
  return data;
};
