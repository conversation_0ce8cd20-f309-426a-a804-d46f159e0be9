import { useState } from 'react';
import PropTypes from 'prop-types';
import {
  propSatisfies, isEmpty, reduce, or, map, compose, path, prop, find, propEq,
} from 'ramda';
import { useTranslation } from 'react-i18next';
import { isNotNil, isEmptyOrNil } from 'lib/utils';
import renderOnlyWhen from 'lib/hocs/render-only-when';
import Dialog from 'lib/components/Dialog';
import DialogTitle from 'lib/components/DialogTitle';
import EditorDialogRow from 'lib/components/EditorDialogRow';
import InputView from 'lib/components/InputView';
import DialogConfirmActions from 'lib/components/DialogConfirmActions';
import ConfirmationDialogView from 'lib/components/ConfirmationDialogView';
import CurrencyPair from 'lib/components/CurrencyPair';
import CPSelector from 'lib/components/CurrencyPairSelector';
import { Body, TextWrapper } from './Editor.Styles';
import BUSelector from './BUSelector';

const isAnyEmpty = compose(
  reduce(or, false),
  map(isEmptyOrNil),
);

const PVTOverridesEditor = ({
  item,
  cancel,
  update,
  cpsByBU,
  displayNames,
}) => {
  const { t } = useTranslation();
  const isOpen = isNotNil(item);
  const [pvt, setPvt] = useState(item?.pvt);
  const [isPvtError, setPvtError] = useState(false);
  const [isOpenConfirm, setOpenConfirm] = useState(false);
  const [buId, setBuId] = useState(item?.buId || '');
  const [cpId, setCpId] = useState(item?.currencyPairId);
  const cps = path([buId], cpsByBU) || [];
  const pvtDecimals = compose(
    prop('displayDecimals'),
    find(propEq(cpId, 'id')),
  )(cps);
  return (
    <>
      <Dialog id="pvtOverrides.edit" open={isOpen} onClose={cancel} maxWidth="md">
        <DialogTitle title="PVT_OVERRIDES" showCancel onClose={cancel} />
        <Body>
          <EditorDialogRow
            title="buId"
            child={item?.buId ? (
              <TextWrapper>{item?.buName}</TextWrapper>
            ) : (
              <TextWrapper>
                <BUSelector
                  singleOptionAutofill
                  selectorType="default"
                  size="small"
                  sx={{ width: 400 }}
                  value={buId}
                  setValue={(value) => {
                    setBuId(value);
                    setCpId(null);
                  }}
                />
              </TextWrapper>
            )}
          />

          <EditorDialogRow
            title="currencyPair"
            child={item?.currencyPairId ? (
              <TextWrapper>
                <CurrencyPair
                  id={item?.currencyPairId}
                  displayName={item?.ccyPairDisplayName}
                />
              </TextWrapper>
            ) : (
              <CPSelector
                value={cpId || []}
                setValue={(value) => setCpId(value?.id)}
                ccyPairs={cps || []}
                displayNames={displayNames}
                showTitle={false}
              />
            )}
          />

          <EditorDialogRow
            title="pvt"
            child={(
              <InputView
                id="pvtOverride.edit.pvt"
                value={pvt}
                maxDecimals={pvtDecimals}
                onChange={(newValue, hasError) => {
                  setPvt(newValue);
                  setPvtError(hasError || isEmpty(newValue));
                }}
              />
            )}
          />

        </Body>
        <DialogConfirmActions
          id="distributionOverride.edit.actions"
          onConfirm={() => setOpenConfirm(true)}
          confirmLabel="generic.action.update"
          onCancel={cancel}
          cancelLabel="defaultButton.cancel"
          disabled={isAnyEmpty([buId, cpId, pvt]) || isPvtError}
        />
      </Dialog>
      <ConfirmationDialogView
        id="modal.pvtOverride.edit"
        title={t('fatFinger.title', { actionLabel: 'pvtOverride.edit' })}
        subTitle={t('pvtOverride.edit.confirm', {
          buName: buId,
          ccyPairDisplayName: prop(cpId, displayNames),
          pvt,
        })}
        isOpen={isOpenConfirm}
        onConfirm={() => update({
          buId,
          cpId,
          pvt,
        })}
        onCancel={() => setOpenConfirm(false)}
      />
    </>
  );
};

PVTOverridesEditor.defaultProps = {
  item: null,
  cpsByBU: [],
};

PVTOverridesEditor.propTypes = {
  item: PropTypes.object,
  cancel: PropTypes.func.isRequired,
  update: PropTypes.func.isRequired,
  cpsByBU: PropTypes.object,
  displayNames: PropTypes.object.isRequired,
};

export default renderOnlyWhen(propSatisfies(isNotNil, 'item'))(PVTOverridesEditor);
