import qs from 'qs';
import axios from 'api/getAxios';

export default async (instruction) => {
  const {
    aggregationInstructionId,
    buId,
    currencyPairId,
    channel,
    maxPosition,
    maxTimeOpen,
    maxMarketDeviation,
  } = instruction;
  const { data } = await axios({
    method: 'put',
    url: '/booking-aggregation-instruction',
    data: qs.stringify({
      aggregationInstructionId,
      buId,
      currencyPairId,
      channel,
      maxPosition,
      maxTimeOpen,
      maxMarketDeviation,
    }),
  });
  return data;
};
