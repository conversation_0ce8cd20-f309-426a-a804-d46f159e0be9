import styled from 'styled-components';
import Box from '@mui/material/Box';
import Button from 'lib/components/Button';
import { colors, fonts } from 'lib/theme/theme';

export const Wrapper = styled(Box)`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  gap: 0.1rem;
  margin-top: 0.1rem;
  align-items: flex-start;
`;

export const HeaderWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 2rem;
  width: 50%;
  min-width: 800px;
`;

export const ActionWrapper = styled.div`
  display: flex;
`;

export const StyledButton = styled(Button)`
&& {
  font-size: ${fonts.mediumTitle.size};
  height: 3.5rem;
  margin-top: 6px;
  white-space: nowrap;
  &:hover {
    background: ${colors.lightGrayBackground};
  }
}
`;

export const SelectorWrapper = styled.div`
  flex-grow: 8;
`;

export const ButtonWrapper = styled.div`
  flex-grow: 1;
`;
