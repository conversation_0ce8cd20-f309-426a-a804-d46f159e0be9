global

defaults

  timeout connect 5000
  timeout check 5000
  timeout client 30000
  timeout server 30000

frontend stats
  bind *:4444
  mode http
  stats uri /
  stats show-legends
  stats enable
  stats auth admin:adminpwd

frontend customer-ui
  bind :8433
  mode http
  default_backend customer-ui-servers

frontend dealer-ui
  bind :8434
  mode http
  default_backend dealer-ui-servers

frontend pricing-fixsessions
  bind :9436
  mode tcp
  default_backend pricing-fix-servers

frontend trading-fixsessions
  bind :9435
  mode tcp
  default_backend trading-fix-servers

frontend um-grpc
  bind :4435 proto h2
  mode http
  default_backend um-grpc-servers

peers sticktables
  bind :10000
  server wta-loadbalancer-1
  table sticky-sessions type ip size 1m

backend customer-ui-servers
  mode http
  balance roundrobin
  option httpchk GET /api/environment
  cookie CUSTOMERUNITCOOKIE insert indirect nocache
  server customer_server_1 localhost:8113 check cookie unit1
  server customer_server_2 localhost:8123 check cookie unit2

backend dealer-ui-servers
  mode http
  balance roundrobin
  option httpchk GET /api/environment
  cookie DEALERUNITCOOKIE insert indirect nocache
  server dealer_server_1 localhost:8114 check cookie unit1
  server dealer_server_2 localhost:8124 check cookie unit2

backend pricing-fix-servers
  mode tcp
  balance roundrobin
  server pricing_fix_session_1 localhost:9110 check
  server pricing_fix_session_2 localhost:9120 check

backend trading-fix-servers
  mode tcp
  balance roundrobin
  server trading_fix_session_1 localhost:9111 check
  server trading_fix_session_2 localhost:9121 check

backend um-grpc-servers
  mode http
  balance roundrobin
  stick match src table sticktables/sticky-sessions
  stick store-request src table sticktables/sticky-sessions
  server um_grpc_server_1 localhost:4115 check proto h2
  server um_grpc_server_2 localhost:4125 check proto h2
