import axios from 'api/getAxios';
import { encodeCurrencyPair } from 'lib/utils';

export default async (currencyPairId, basePriceComputation) => {
  const encodedCcyPairId = encodeCurrencyPair(currencyPairId);
  const url = `/currency-pair/${encodedCcyPairId}/base-price-computation`;
  const { data } = await axios({
    headers: { 'Content-Type': 'text/plain' },
    method: 'put',
    url,
    data: basePriceComputation,
  });
  return data;
};
