This image is based on a Java 17 runtime by Eclipse Temurin project

It exposes a /apps folder, where the user should mount a folder with the following structure:

```shell
runtime/
├── apps/
│   ├── wta4/
│   │   ├── config/
│   │   │   ├──application.properties
│   │   │   └──logback.xml
│   │   ├── logs/
│   │   ├── var/
│   │   └── wta4-xx.yy.jar
│   ├── wta4-dealer-ui/
│   │   ├── config/
│   │   │   ├──application.properties
│   │   │   └──logback.xml
│   │   ├── logs/
│   │   └── wta4-dealer-ui-xx.yy.jar
│   └── wta4ui/
│       ├── config/
│       │   ├──application.properties
│       │   └──logback.xml
│       ├── logs/
│       └── wta4ui-xx.yy.jar
└── docker-compose.yaml
```

The image base process is a launcher script that will look for the WTA core, Dealer UI and Customer UI jars in their respective folders, and start them in the background by executing its run.sh script. It will keep running, waiting until any of the applications stop.

If any app jar is not found, it will just skip it, to support development environments where any app can be launched locally from an IDE

[WARNING] If you are setting up this env in Windows:  start.sh file format should be changed from CRLF to LF

To create the image:
```shell
docker build -t wtaunit .

Before starting the WTA4 application, we will need to connect to the database container to the docker network:

 Syntax: docker network connect <network_name> <container_name>
 
 Example: docker network connect wtaunit wtadb
```
