services:

  wtaunit-1:
    container_name: wtaunit-1
    image: wtaunit:latest
    environment:
      - instanceId=1
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=30s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
    volumes:
      - /srv/docker-data/wtaunit/apps:/apps
    ports:
      - 7090:8090
      - 7091:8091
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - wtaunit

networks:
  wtaunit:
    name: wtaunit
