name: wtadev
services:

  ld1:
    container_name: wtald1
    image: wtaunit:latest
    environment:
      - instanceId=1
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=30s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
    volumes:
      - /srv/docker-data/wtaunit/apps:/apps
      - /srv/docker-data/wtaunit/var/wta4-ld-01:/apps/wta4/var
    ports:
      - 7090:8090
      - 7091:8091
      - 7092:9900
      - 7093:9901
      - 7094:9898
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - wtadev

  ld2:
    container_name: wtald2
    image: wtaunit:latest
    environment:
      - instanceId=2
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=30s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
    volumes:
      - /srv/docker-data/wtaunit/apps:/apps
      - /srv/docker-data/wtaunit/var/wta4-ld-02:/apps/wta4/var
    ports:
      - 7190:8090
      - 7191:8091
      - 7192:9900
      - 7193:9901
      - 7194:9898
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - wtadev

  eventbus:
    container_name: wtaeb
    image: apache/activemq-artemis:2.39.0
    environment:
      - ARTEMIS_USER=admin
      - ARTEMIS_PASSWORD=jamgo
      - ANONYMOUS_LOGIN=true
    volumes:
      - /srv/docker-data/wtaunit/event-bus:/var/lib/artemis-instance
    ports:
      - 7161:8161
      - 61616:61616
    networks:
      - wtadev

  loadbalancer:
    container_name: wtalb
    image: haproxy:lts-alpine
    network_mode: host
    volumes:
      - /srv/docker-data/wtaunit/load-balancer:/usr/local/etc/haproxy
    extra_hosts:
      - host.docker.internal:host-gateway

networks:
  wtadev:
    name: wtadev
