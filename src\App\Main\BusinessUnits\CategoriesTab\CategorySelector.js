import PropTypes from 'prop-types';
import { pluck } from 'ramda';
import SearchableSelector from 'lib/components/SearchableSelector';

const CategorySelector = ({ categories, value, setValue }) => {
  const categoryIds = pluck('id', categories);
  return (
    <SearchableSelector
      title="CATEGORIES"
      value={value}
      options={categoryIds}
      setValue={setValue}
    />
  );
};

CategorySelector.defaultProps = {
  value: '',
};

CategorySelector.propTypes = {
  categories: PropTypes.arrayOf(PropTypes.object).isRequired,
  value: PropTypes.string,
  setValue: PropTypes.func.isRequired,
};

export default CategorySelector;
