logging.config=/apps/logback.xml

server.port=8115

rmiJMXPort=5115

spring.datasource.url=***************************************************
spring.datasource.username=root
spring.datasource.password=sys123

fix.adapter.config=
fix.adapter.streamingPort=9110
fix.adapter.tradingPort=9111

grpc.port=4115
grpc.secretsByClientId={\
  'WTA4-UI':'wta4ui-secret-sdfsa897a09fasdfas0df9a70sd98f70as9df70as97df0a9sd',\
  'WTA4-DEALER-UI':'dealer-ui-secret-4398523kjsadhfglksjdfyghwe8945t7we'\
}

#Keycloak client: Customer UI in wtaunit docker
# Client id and secret - match the client id / secret in Keycloak realm config
oidc.clients.WTA4-UI.clientId=wta4ui
oidc.clients.WTA4-UI.clientSecret=9alUKpiNtSLxWmG3SKci4UHQGFXGPifx
# Keycloak URLs reachable from the browser
oidc.clients.WTA4-UI.issuer=http://localhost:8880/realms/mksdev
oidc.clients.WTA4-UI.authorizationUri=http://localhost:8880/realms/mksdev/protocol/openid-connect/auth
oidc.clients.WTA4-UI.logoutUri=http://localhost:8880/realms/mksdev/protocol/openid-connect/logout?client_id=wta4ui
oidc.clients.WTA4-UI.accountManagementUri=http://localhost:8880/realms/mksdev/account/account-security/signing-in
# Keycloak URLs reachable from WTA4
oidc.clients.WTA4-UI.keyDiscoveryUri=http://localhost:8880/realms/mksdev/protocol/openid-connect/certs
oidc.clients.WTA4-UI.accessTokenUri=http://localhost:8880/realms/mksdev/protocol/openid-connect/token
# Application URLs reachable from the browser
oidc.clients.WTA4-UI.redirectUri=http://localhost:8433/login-oauth2

#Keycloak client: Dealer UI in wtaunit docker
# Client id and secret - match the client id / secret in Keycloak realm config
oidc.clients.WTA4-DEALER-UI.clientId=wta4-dealer-ui
oidc.clients.WTA4-DEALER-UI.clientSecret=afB52BrDQsjXUWqu7D7QrbCXOgAFuRGs
# Keycloak URLs reachable from the browser
oidc.clients.WTA4-DEALER-UI.issuer=http://localhost:8880/realms/mksdev
oidc.clients.WTA4-DEALER-UI.authorizationUri=http://localhost:8880/realms/mksdev/protocol/openid-connect/auth
oidc.clients.WTA4-DEALER-UI.logoutUri=http://localhost:8880/realms/mksdev/protocol/openid-connect/logout?client_id=wta4-dealer-ui
oidc.clients.WTA4-DEALER-UI.accountManagementUri=http://localhost:8880/realms/mksdev/account/account-security/signing-in
# Keycloak URLs reachable from WTA4
oidc.clients.WTA4-DEALER-UI.keyDiscoveryUri=http://localhost:8880/realms/mksdev/protocol/openid-connect/certs
oidc.clients.WTA4-DEALER-UI.accessTokenUri=http://localhost:8880/realms/mksdev/protocol/openid-connect/token
# Application URLs reachable from the browser
oidc.clients.WTA4-DEALER-UI.redirectUri=http://localhost:8434/login-oauth2

# Event bus
eventBus.enabled=true
eventBus.url=tcp://localhost:61616

amq.connectorUrl=tcp://localhost:6666
amq.stompConnectorUrl=tcp://localhost:7777
