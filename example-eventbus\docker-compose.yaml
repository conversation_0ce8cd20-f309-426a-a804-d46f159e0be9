name: wtadocker
services:

  wtaunit-1:
    container_name: wtaunit-1
    image: wtaunit:latest
    environment:
      - instanceId=1
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=30s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
      - WTA4UI_HOST_PORT=7090
      - WTA4DUI_HOST_PORT=7091
    volumes:
      - /srv/docker-data/wtaunit/apps:/apps
      - /srv/docker-data/wtaunit/var/wta4-ld-01:/apps/wta4/var
    ports:
      - 7090:8090
      - 7091:8091
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - wta

  wtaunit-2:
    container_name: wtaunit-2
    image: wtaunit:latest
    environment:
      - instanceId=2
      - regionId=ld
      - WTA4_STARTED_WAIT_TIME=30s
      - WTA4_JAVA_ARGS=-DignoreMarketSchedule -DsimulateFindur -DsimulateLP
      - WTA4UI_JAVA_ARGS=-Dinstance=wta
      - WTA4UI_HOST_PORT=7190
      - WTA4DUI_HOST_PORT=7191
    volumes:
      - /srv/docker-data/wtaunit/apps:/apps
      - /srv/docker-data/wtaunit/var/wta4-ld-02:/apps/wta4/var
    ports:
      - 7190:8090
      - 7191:8091
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - wta

  wtaeb:
    container_name: wtaeb
    image: apache/activemq-artemis:2.39.0
    environment:
      - ARTEMIS_USER=admin
      - ARTEMIS_PASSWORD=jamgo
      - ANONYMOUS_LOGIN=true
    volumes:
      - /srv/docker-data/wtaunit/event-bus:/var/lib/artemis-instance
    ports:
      - 7161:8161
      - 61616:61616
    networks:
      - wta

networks:
  wta:
    name: wta
