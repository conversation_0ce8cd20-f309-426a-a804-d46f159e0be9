import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getPvtOverrideEditor, closePvtOverrideEditor, updatePvtOverride } from 'modules/busConfig';
import { getCurrencyPairsByBuId } from 'modules/masterData';
import { getCurrencyPairsDisplayNames } from 'modules/user';
import Component from './Editor.Component';

const mapStateToProps = createStructuredSelector({
  item: getPvtOverrideEditor,
  cpsByBU: getCurrencyPairsByBuId,
  displayNames: getCurrencyPairsDisplayNames,
});

const mapDispatchToProps = {
  cancel: closePvtOverrideEditor,
  update: updatePvtOverride,
};

export default connect(mapStateToProps, mapDispatchToProps)(Component);
