import { stringify } from 'qs';
import axios from 'api/getAxios';

export default async (
  onlyActive,
  onlyDeals,
  onlyAggregatedBookings,
  startDate,
  endDate,
  timeZone,
) => {
  const params = stringify({
    onlyActive,
    onlyDeals,
    onlyAggregatedBookings,
    startDate,
    endDate,
    timeZone,
  }, {
    skipNulls: true,
  });
  const url = `/orders${params && `?${params}`}`;
  const { data } = await axios.get(url, {
    timeout: 120000,
  });
  return data;
};
