import PropTypes from 'prop-types';
import { prop } from 'ramda';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import ActionBtn from 'lib/components/GenericTable/Table/Cell/EditButton';
import renderOnlyWhen from 'lib/hocs/render-only-when';
import { ActionGroup } from './Actions.Styles';

const Actions = ({
  row, openEditor, openDeleteDialog,
}) => (
  <ActionGroup>
    <ActionBtn
      id="pvtOverride.edit"
      onClick={openEditor}
      row={row}
    />
    <ActionBtn
      id="pvtOverride.delete"
      icon={<DeleteOutlineOutlinedIcon sx={{ fontSize: 20 }} />}
      onClick={openDeleteDialog}
      row={row}
    />
  </ActionGroup>
);

Actions.propTypes = {
  openEditor: PropTypes.func.isRequired,
  openDeleteDialog: PropTypes.func.isRequired,
  row: PropTypes.object.isRequired,
};

export default renderOnlyWhen(prop('canShowEdit'))(Actions);
